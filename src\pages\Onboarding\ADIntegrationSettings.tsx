
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Check, X } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const ADIntegrationSettings = () => {
  const [connectionStatus, setConnectionStatus] = useState('connected');
  const [lastSync, setLastSync] = useState('2024-01-20 14:30');
  
  const { register, formState: { errors } } = useForm({
    defaultValues: {
      domainName: 'example.com',
      baseDN: 'DC=example,DC=com',
      username: 'CN=Admin,DC=example,DC=com',
      password: '',
      secureConnection: false,
      portNumber: '636',
      enableAutoSync: false,
      frequency: '',
      syncUsers: true,
      syncGroups: false
    }
  });

  const navigate = useNavigate();


  const handleTestConnection = async () => {
    setConnectionStatus('testing');
    console.log('Testing connection...');
    
    setTimeout(() => {
      setConnectionStatus('connected');
      setLastSync(new Date().toISOString().slice(0, 16).replace('T', ' '));
    }, 2000);
  };

  const handleBack = () => {
    navigate('/onboarding/add-employee');
    console.log('Navigate back');
    // TODO: Add navigation logic
  };

const handleConfirmAndImport = async () => {
    try {
      console.log('Confirm and import');
      navigate('/onboarding/logo-upload');
    } catch (error) {
      console.error('Failed to import:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">

      <header className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-10 pb-6">
          <div className="flex justify-between items-center mt-8">
            <div className="flex flex-col">
              <div className="flex items-center mb-2">
                <div className="w-14 h-14 mr-3">
                  <img
                    src="/expenso-logo.png"
                    alt="Expenso Logo"
                    className="w-full h-full"
                  />
                </div>

                <div className="w-50 h-10">
                  <img
                    src="/expenso-text.svg"
                    alt="EXPENSO"
                    className="w-full h-full"
                  />
                </div>
              </div>

              <p className="text-xs font-medium text-gray-500 uppercase tracking-[0.9em]">
                Admin Dashboard
              </p>
            </div>

            <div className="flex flex-col items-start space-y-1">
              <p className="text-sm font-semibold text-gray-900">
                Setup Progress
              </p>
              <p className="text-xs text-gray-500">2 of 4 steps completed</p>
              <div className="flex space-x-2">
                {[1, 2, 3, 4].map((step) => (
                  <div
                    key={step}
                    className={`h-2 w-12 rounded-full ${
                      step <= 2 ? "bg-[#06B217]" : "bg-gray-200"
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>

          <div className="flex items-center mt-10">
            <h1 className="text-3xl font-semibold text-gray-600">AD Integration Settings</h1>
          </div>
          
          <p className="text-gray-600 mt-4 text-base">
            Configure Active Directory integration to streamline user management and access control.
          </p>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
        <div className="bg-white rounded-lg p-10">
          <div className="space-y-12">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Connection Status</h2>
              <div className="flex items-center space-x-3">
                <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                  connectionStatus === 'connected' ? 'bg-green-100' : 'bg-red-100'
                }`}>
                  {connectionStatus === 'connected' ? (
                    <Check className="w-4 h-4 text-green-600" />
                  ) : (
                    <X className="w-4 h-4 text-red-600" />
                  )}
                </div>
                <div>
                  <p className={`font-medium ${
                    connectionStatus === 'connected' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {connectionStatus === 'connected' ? 'Connected' : 'Disconnected'}
                  </p>
                  {connectionStatus === 'connected' && (
                    <p className="text-sm text-gray-500">
                      Last successful sync: {lastSync}
                    </p>
                  )}
                </div>
              </div>
            </div>

            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Configuration</h2>
              <div className="max-w-lg space-y-6">
                <div>
                  <label className="block text-base font-medium text-gray-800 mb-3">
                    Domain Name/LDAP Server Address
                  </label>
                  <input
                    type="text"
                    {...register('domainName', { required: 'Domain name is required' })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors text-base bg-gray-50"
                    value="example.com"
                    readOnly
                  />
                  {errors.domainName && (
                    <p className="mt-1 text-sm text-red-600">{errors.domainName.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-base font-medium text-gray-800 mb-3">
                    Base DN
                  </label>
                  <input
                    type="text"
                    {...register('baseDN', { required: 'Base DN is required' })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors text-base bg-gray-50"
                    value="DC=example,DC=com"
                    readOnly
                  />
                  {errors.baseDN && (
                    <p className="mt-1 text-sm text-red-600">{errors.baseDN.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-base font-medium text-gray-800 mb-3">
                    Username/Bind DN
                  </label>
                  <input
                    type="text"
                    {...register('username', { required: 'Username is required' })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors text-base bg-gray-50"
                    value="CN=Admin,DC=example,DC=com"
                    readOnly
                  />
                  {errors.username && (
                    <p className="mt-1 text-sm text-red-600">{errors.username.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-base font-medium text-gray-800 mb-3">
                    Password
                  </label>
                  <input
                    type="password"
                    {...register('password', { required: 'Password is required' })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors text-base bg-gray-50"
                    readOnly
                  />
                  {errors.password && (
                    <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
                  )}
                </div>

                <div>
                  <div className="flex items-center justify-between">
                    <label className="block text-base font-medium text-gray-800">
                      Secure Connection (SSL/TLS)
                    </label>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        {...register('secureConnection')}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                    </label>
                  </div>
                </div>

                <div>
                  <label className="block text-base font-medium text-gray-800 mb-3">
                    Port Number
                  </label>
                  <input
                    type="text"
                    {...register('portNumber', { required: 'Port number is required' })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors text-base bg-gray-50"
                    value="636"
                    readOnly
                  />
                  {errors.portNumber && (
                    <p className="mt-1 text-sm text-red-600">{errors.portNumber.message}</p>
                  )}
                </div>

                <div>
                  <button
                    type="button"
                    onClick={handleTestConnection}
                    disabled={connectionStatus === 'testing'}
                    className="px-6 py-3 bg-gray-800 text-white font-medium rounded-lg hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-800 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-base"
                  >
                    {connectionStatus === 'testing' ? 'Testing...' : 'Test Connection'}
                  </button>
                </div>
              </div>
            </div>

            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Synchronization Settings</h2>
              <div className="max-w-lg space-y-6">
                <div>
                  <div className="flex items-center justify-between">
                    <label className="block text-base font-medium text-gray-800">
                      Enable Automatic Synchronization
                    </label>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        {...register('enableAutoSync')}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                    </label>
                  </div>
                </div>

                <div>
                  <label className="block text-base font-medium text-gray-800 mb-3">
                    Frequency
                  </label>
                  <input
                    type="text"
                    {...register('frequency')}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors text-base bg-gray-50"
                    readOnly
                  />
                </div>

                <div>
                  <div className="flex items-center justify-between">
                    <label className="block text-base font-medium text-gray-800">
                      Synchronize Users
                    </label>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        {...register('syncUsers')}
                        className="sr-only peer"
                        defaultChecked
                      />
                      <div className="w-11 h-6 bg-teal-500 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500 after:translate-x-full"></div>
                    </label>
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between">
                    <label className="block text-base font-medium text-gray-800">
                      Synchronize Groups
                    </label>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        {...register('syncGroups')}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-between pt-8">
              <button
                type="button"
                onClick={handleBack}
                className="px-8 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors text-base"
              >
                Back
              </button>
              <button
                type="button"
                onClick={handleConfirmAndImport}
                className="px-8 py-3 bg-teal-500 text-white font-medium rounded-lg hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 transition-colors text-base"
              >
                Confirm and Import
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ADIntegrationSettings;