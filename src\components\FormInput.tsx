import { ReactNode } from "react";

interface FormInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  fullWidth?: boolean;
  icon?: ReactNode;
  type?: string;
}

export default function FormInput({
  value,
  onChange,
  placeholder,
  className = "",
  fullWidth = false,
  icon,
  type = "text",
}: FormInputProps) {
  return (
    <div className={`relative inline-block ${fullWidth ? "w-full" : "w-fit"} ${className}`}>
      <input
        type={type}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className={`flex items-center justify-between gap-8 px-6 py-3 rounded-full bg-white shadow-sm transition text-sm w-full ${
          value ? "text-gray-700" : "text-gray-500"
        } placeholder-gray-500 border-0 focus:outline-none focus:ring-0`}
      />
      {icon && (
        <div className="absolute right-6 top-1/2 transform -translate-y-1/2 text-gray-400">
          {icon}
        </div>
      )}
    </div>
  );
}