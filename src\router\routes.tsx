import Layout from "../components/layout/MainLayout";
import AuditTrail from "../pages/AuditTrail/AuditTrail";
import Categories from "../pages/Categories/Categories";
import ExpenseReports from "../pages/ExpenseReports/ExpenseReports";
import ReportDetails from "../pages/ExpenseReports/ReportDetails";
import Home from "../pages/Home/Home";
import Integrations from "../pages/Integrations/Integrations";
import AddEmployees from "../pages/Onboarding/AddEmployee";
import AddEmployee from "../pages/Onboarding/AddEmployeeForm";
import ADIntegrationSettings from "../pages/Onboarding/ADIntegrationSettings";
import ImportEmployees from "../pages/Onboarding/ImportEmployee";
import LogoUpload from "../pages/Onboarding/LogoUpload";
import OrganizationProfile from "../pages/Onboarding/OrganizationProfile";
import OrganizationReview from "../pages/Onboarding/OrganizationReview";
import SetupDone from "../pages/Onboarding/SetupDone";
import PolicyManagement from "../pages/PolicyManagement/PolicyManagement";
import AddPolicy from "../pages/PolicyManagement/components/AddPolicy";
import Settings from "../pages/Settings/Settings";
import UserManagement from "../pages/UserManagement/UserManagement";
import Login from "../pages/Login/Login";
import Signup from "../pages/signup/SignUp";
import AuthPageLayout from "../components/layout/AuthLayout";
import NotFound from "../pages/NotFound/NotFound";


export const routes = [
    {
        path: "/add-employee",
        element: <AddEmployee />,
    },
    {
        path: "/add-employee-form",
        element: <AddEmployees />,
    },
    {
        path: "/ad-integration-settings",
        element: <ADIntegrationSettings />,
    },
    {
        path: "/import-employees",
        element: <ImportEmployees />,
    },
    {
        path: "/logo-upload",
        element: <LogoUpload />,
    },
    {
        path: "/profile",
        element: <OrganizationProfile />,
    },
    {
        path: "/review",
        element: <OrganizationReview />,
    },
    {
        path: "/setup-complete",
        element: <SetupDone />,
    },
    {
        path: "/auth",
        element: <AuthPageLayout />,
        children: [
            {
                path: 'login',
                element: <Login />,
            },
            {
                path: 'signup',
                element: <Signup />,
            },
        ],
    },
    {
        path: "/",
        element: <Layout />,
        children: [
            {
                index: true,
                element: <Home />,
            },
            {
                path: "users",
                element: <UserManagement />,
            },
            {
                path: "expense-reports",
                element: <ExpenseReports />,
            },
            {
                path: "expense-reports/:employeeId",
                element: <ReportDetails />,
            },
            {
                path: "employee-expense-report",
                element: <ExpenseReports />,
            },
            {
                path: "policies",
                element: <PolicyManagement />,
            },
            {
                path: "policies/add",
                element: <AddPolicy />,
            },
            {
                path: "categories",
                element: <Categories />,
            },
            {
                path: "integrations",
                element: <Integrations />,
            },
            {
                path: "audit-trail",
                element: <AuditTrail />,
            },
            {
                path: "settings",
                element: <Settings />,
            },
        ],
    },
    {
        path: "*",
        element: <NotFound />,
    },
];